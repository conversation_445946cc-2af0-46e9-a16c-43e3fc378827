import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckIcon, XIcon } from "lucide-react";
import { Section } from "@/types/blocks/section";

interface ComparisonProps {
  section: Section;
}

export default function Comparison({ section }: ComparisonProps) {
  if (section.disabled) {
    return null;
  }

  // Extract products (first 3 items) and features (remaining items)
  const products = section.items?.slice(0, 3) || [];
  const features = section.items?.slice(3) || [];

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === "boolean") {
      return value ? (
        <CheckIcon className="h-5 w-5 text-green-500" />
      ) : (
        <XIcon className="h-5 w-5 text-red-400" />
      );
    }
    return <span className="text-sm text-muted-foreground">{value}</span>;
  };

  return (
    <section className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="mx-auto max-w-[58rem] text-center">
          <h2 className="text-3xl font-bold leading-[1.1] sm:text-3xl md:text-6xl">
            {section.title}
          </h2>
          {section.description && (
            <p
              className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7 mx-auto mt-4"
              dangerouslySetInnerHTML={{ __html: section.description }}
            />
          )}
        </div>

        <div className="mx-auto mt-16 max-w-6xl">
          <div className="overflow-x-auto">
            <div className="min-w-full">
              {/* Product Headers */}
              <div className="grid grid-cols-4 gap-4 mb-8">
                <div className="text-left flex items-end h-33 mt-3 pb-4">
                  <h3 className="text-lg font-semibold">Features</h3>
                </div>
                {products.map((product, index: number) => (
                  <div key={index} className="relative">
                    {product.badge && (
                      <Badge
                        variant={product.badgeVariant || "default"}
                        className="absolute left-1/2 -translate-x-1/2 z-10"
                      >
                        {product.badge}
                      </Badge>
                    )}
                    <Card
                      className={`mt-3 h-33 flex flex-col ${
                        product.highlight
                          ? "ring-1 ring-primary border-primary"
                          : ""
                      }`}
                    >
                      <CardContent className="p-4 text-center flex-1 flex flex-col justify-between">
                        <div className="flex flex-col justify-center pb-1">
                          <h3 className="text-lg font-bold">{product.title}</h3>
                          <p className="text-xs text-muted-foreground">
                            {product.tagline}
                          </p>
                        </div>
                        {/* Only show button for AI Build (first product) */}
                        {index === 0 && product.buttons?.[0] && (
                          <Button
                            variant={product.buttons[0].variant || "default"}
                            className="w-full"
                            size="sm"
                            asChild
                          >
                            <a
                              href={product.buttons[0].url}
                              target={product.buttons[0].target || "_blank"}
                              rel="noopener noreferrer"
                            >
                              {product.buttons[0].title}
                            </a>
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>

              {/* Feature Comparison */}
              <div className="space-y-2">
                {features.map((feature, index: number) => (
                  <div
                    key={index}
                    className="grid grid-cols-4 gap-4 py-4 px-2 rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="font-medium text-left">{feature.title}</div>
                    <div className="flex justify-center">
                      {renderFeatureValue(feature.features?.aibuild || false)}
                    </div>
                    <div className="flex justify-center">
                      {renderFeatureValue(feature.features?.shipfast || false)}
                    </div>
                    <div className="flex justify-center">
                      {renderFeatureValue(feature.features?.shipany || false)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Mobile-friendly version */}
              <div className="md:hidden space-y-6 mt-8">
                {products.map((product, productIndex: number) => (
                  <Card
                    key={productIndex}
                    className={
                      product.highlight
                        ? "ring-1 ring-primary border-primary"
                        : ""
                    }
                  >
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        {product.badge && (
                          <Badge
                            variant={product.badgeVariant || "default"}
                            className="mb-2"
                          >
                            {product.badge}
                          </Badge>
                        )}
                        <h3 className="text-xl font-bold">{product.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          {product.tagline}
                        </p>
                        {/* Only show button for AI Build (first product) */}
                        {productIndex === 0 && product.buttons?.[0] && (
                          <Button
                            variant={product.buttons[0].variant || "default"}
                            className="w-full"
                            asChild
                          >
                            <a
                              href={product.buttons[0].url}
                              target={product.buttons[0].target || "_blank"}
                              rel="noopener noreferrer"
                            >
                              {product.buttons[0].title}
                            </a>
                          </Button>
                        )}
                      </div>
                      <div className="space-y-3">
                        {features.map((feature, featureIndex: number) => {
                          const productKey =
                            productIndex === 0
                              ? "aibuild"
                              : productIndex === 1
                              ? "shipfast"
                              : "shipany";
                          const value = feature.features?.[productKey] || false;
                          return (
                            <div
                              key={featureIndex}
                              className="flex justify-between items-center"
                            >
                              <span className="text-sm">{feature.title}</span>
                              {renderFeatureValue(value)}
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
