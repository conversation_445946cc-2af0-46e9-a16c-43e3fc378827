import { Section as SectionType } from "@/types/blocks/section";
import CTAButton from "@/components/ui/ctabutton";

export default function CTA({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="px-8">
        <div className='flex items-center justify-center rounded-2xl  bg-[url("/imgs/masks/circle.svg")] bg-cover bg-center px-8 py-12 text-center md:p-16'>
          <div className="mx-auto max-w-(--breakpoint-md)">
            <h2 className="mb-4 text-balance text-3xl font-semibold md:text-5xl">
              {section.title}
            </h2>
            <p
              className="m mx-auto max-w-3xl text-muted-foreground lg:text-xl"
              dangerouslySetInnerHTML={{ __html: section.description || "" }}
            />
            {section.buttons && (
              <div className="mt-8 flex flex-col justify-center gap-6 sm:flex-row">
                {section.buttons.map((item, idx) => (
                  <CTAButton
                    key={idx}
                    title={item.title || ""}
                    url={item.url || ""}
                    target={item.target}
                    variant={item.variant as any}
                    icon={item.icon}
                    showPrePurchaseDialog={item.showPrePurchaseDialog}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
