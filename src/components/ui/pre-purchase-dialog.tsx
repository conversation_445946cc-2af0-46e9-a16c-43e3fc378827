"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface PrePurchaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  redirectUrl: string;
  onConfirm?: () => void;
}

export default function PrePurchaseDialog({
  open,
  onOpenChange,
  redirectUrl,
  onConfirm,
}: PrePurchaseDialogProps) {
  const [acknowledged, setAcknowledged] = useState(false);
  const router = useRouter();

  const handleAgreeAndPay = () => {
    if (acknowledged) {
      if (onConfirm) {
        onConfirm();
      } else {
        onOpenChange(false);
        router.push(redirectUrl);
      }
    }
  };

  const handleCancel = () => {
    setAcknowledged(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-orange-600">
            ⚠️ Important Pre-Purchase Information
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Please read the following information carefully before proceeding
            with your purchase.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto py-4 space-y-4 text-sm">
          <div className="space-y-3">
            <h4 className="font-semibold text-foreground">
              Product Information:
            </h4>
            <ul className="space-y-2 text-muted-foreground">
              <li>
                • This is a digital product delivered instantly after payment
              </li>
              <li>• All sales are final - no refunds will be provided</li>
              <li>
                • You will receive lifetime access to the purchased content
              </li>
              <li>
                • Updates and improvements are included at no additional cost
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-foreground">Payment Terms:</h4>
            <ul className="space-y-2 text-muted-foreground">
              <li>• Payment is processed securely through Stripe</li>
              <li>
                • Prices are shown in USD and may be subject to local taxes
              </li>
              <li>
                • You will receive a receipt via email after successful payment
              </li>
              <li>
                • Access credentials will be sent to your registered email
                address
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-foreground">Usage Rights:</h4>
            <ul className="space-y-2 text-muted-foreground">
              <li>• Licensed for personal and commercial use</li>
              <li>• You may not redistribute or resell the content</li>
              <li>• Sharing access credentials is prohibited</li>
              <li>• Support is provided through our official channels only</li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-foreground">
              Technical Requirements:
            </h4>
            <ul className="space-y-2 text-muted-foreground">
              <li>• Modern web browser with JavaScript enabled</li>
              <li>• Stable internet connection for downloads</li>
              <li>• Basic knowledge of development tools recommended</li>
              <li>• Compatible with Windows, macOS, and Linux</li>
            </ul>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              id="acknowledge"
              checked={acknowledged}
              onCheckedChange={(checked) => setAcknowledged(checked as boolean)}
            />
            <label
              htmlFor="acknowledge"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I acknowledge the above information and agree to the terms
            </label>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              onClick={handleAgreeAndPay}
              disabled={!acknowledged}
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600"
            >
              Agree & Pay
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
